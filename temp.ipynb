import os
import sys
import json
import math
import warnings
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Tuple

import numpy as np
import pandas as pd

import cvxpy as cp

import yfinance as yf

from sklearn.linear_model import Ridge
from sklearn.ensemble import HistGradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.discriminant_analysis import StandardScaler

from matplotlib import pyplot as plt


from lightgbm import LGBMRegressor
from lightgbm import LGBMRanker

from xgboost import XGBRegressor
from xgboost import XGBRanker

from sklearn.ensemble import RandomForestRegressor

from hmmlearn.hmm import GaussianHMM

np.random.seed(42)
warnings.filterwarnings("ignore", category=UserWarning)

data_df = pd.read_csv("./asset/hackathon_sample_v2.csv", parse_dates=["date"])
factors_df = pd.read_csv("./asset/factor_char_list.csv")
mkt_df = pd.read_csv("./asset/mkt_ind.csv")

print(data_df.head())
print(factors_df.head())
print(mkt_df.head())

data_df_vol = data_df.copy()
data_df_vol['vol_3m'] = data_df.sort_values(['permno', 'date']).groupby('permno', group_keys=False)['stock_exret'].rolling(window=3, min_periods=1).std().reset_index(level=0, drop=True) 
data_df_vol['vol_6m'] = data_df.sort_values(['permno', 'date']).groupby('permno', group_keys=False)['stock_exret'].rolling(window=6, min_periods=4).std().reset_index(level=0, drop=True) 
data_df_vol['vol_12m'] = data_df.sort_values(['permno', 'date']).groupby('permno', group_keys=False)['stock_exret'].rolling(window=12, min_periods=10).std().reset_index(level=0, drop=True) 

print(data_df_vol.columns)

import numpy as np
import pandas as pd
import pywt

def _dwt_window_features(x: np.ndarray, wavelet="db4", level=3):
    """
    x: 1D array (windowed series), no NaNs
    returns dict of scalar features from wavelet decomposition
    """
    coeffs = pywt.wavedec(x, wavelet=wavelet, level=level, mode="periodization")
    A = coeffs[0]      # approximation at level L
    Ds = coeffs[1:]    # details D_L, D_{L-1}, ..., D_1 (pywt orders high->low)

    # Energies (power) of detail coeffs by *increasing* frequency D1..DL
    Ds_low_to_high = Ds[::-1]
    feats = {
        "A_mean": float(np.mean(A)),
        "A_std":  float(np.std(A, ddof=1)) if len(A) > 1 else 0.0,
    }
    # Energy and entropy for each detail band
    for i, d in enumerate(Ds_low_to_high, start=1):  # i = 1..L
        energy = float(np.mean(d**2))
        # Shannon entropy (robust to zeros)
        p = np.abs(d)
        p = p / (p.sum() + 1e-12)
        entropy = float(-(p * np.log(p + 1e-12)).sum())
        feats[f"D{i}_energy"]  = energy
        feats[f"D{i}_entropy"] = entropy
    return feats

def add_dwt_features(
    df: pd.DataFrame,
    entity_col: str,          # e.g., "permno" or "ticker"
    time_col: str,            # e.g., "date" (must be sorted ascending per entity)
    series_cols: list,        # e.g., ["prc", "volume", "momentum"]
    window: int = 36,         # monthly data → try 24–36
    wavelet: str = "db4",
    level: int | None = None, # if None, pick by window size
    suffix: str = "dwt"
) -> pd.DataFrame:
    """
    Returns a copy of df with DWT rolling features appended for each series in series_cols.
    Uses a trailing rolling window (no lookahead).
    """
    out = df.copy()
    out = out.sort_values([entity_col, time_col])

    # choose a sensible max level if not provided
    if level is None:
        # level limited by signal length; db4 needs some minimum length
        # this heuristic: up to floor(log2(window)) but cap at 4 to avoid overfragmenting
        level = int(np.clip(np.floor(np.log2(max(window, 2))), 1, 4))

    def _per_group(g: pd.DataFrame) -> pd.DataFrame:
        g = g.copy()
        for col in series_cols:
            # robust: drop-in NaN filler inside rolling window
            s = g[col].astype(float)

            # build rolling features
            def _roll_func(win_vals: np.ndarray):
                # handle NaNs by simple forward/back fill over the window
                v = win_vals.copy()
                if np.isnan(v).any():
                    # local fill (no leakage beyond window)
                    mask = np.isnan(v)
                    if mask.all():
                        return pd.Series(dtype=float)  # will become NaNs
                    # simple fill: replace NaNs with window median
                    med = np.nanmedian(v)
                    v[mask] = med
                feats = _dwt_window_features(v, wavelet=wavelet, level=level)
                return pd.Series(feats)

            feat_df = s.rolling(window=window, min_periods=window).apply(
                lambda x: np.nan, raw=False
            )  # placeholder to get index; we'll compute with .rolling().agg trick below

            # Use .rolling().apply with a wrapper returning multiple outputs via agg
            # Pandas: do once via list of lambdas is clunky; instead, loop and join:
            tmp = s.rolling(window=window, min_periods=window).apply(
                lambda _: 0.0, raw=False
            )  # dummy to get index alignment

            # Actually compute features by iterating windows (fast enough for monthly)
            idx = tmp.index
            A_mean, A_std = [], []
            D_energy = [[] for _ in range(level)]
            D_entropy = [[] for _ in range(level)]

            vals = s.values
            for i in range(len(vals)):
                if i + 1 < window:
                    A_mean.append(np.nan); A_std.append(np.nan)
                    for j in range(level):
                        D_energy[j].append(np.nan); D_entropy[j].append(np.nan)
                    continue
                seg = vals[i - window + 1 : i + 1]
                if np.isnan(seg).any():
                    med = np.nanmedian(seg)
                    if np.isnan(med):
                        # all NaN
                        A_mean.append(np.nan); A_std.append(np.nan)
                        for j in range(level):
                            D_energy[j].append(np.nan); D_entropy[j].append(np.nan)
                        continue
                    seg = np.where(np.isnan(seg), med, seg)

                feats = _dwt_window_features(seg, wavelet=wavelet, level=level)
                A_mean.append(feats["A_mean"]); A_std.append(feats["A_std"])
                for j in range(level):
                    D_energy[j].append(feats[f"D{j+1}_energy"])
                    D_entropy[j].append(feats[f"D{j+1}_entropy"])

            # write columns
            g[f"{col}_{suffix}_Amean_L{level}"] = A_mean
            g[f"{col}_{suffix}_Astd_L{level}"]  = A_std
            for j in range(level):
                g[f"{col}_{suffix}_D{j+1}E"] = D_energy[j]
                g[f"{col}_{suffix}_D{j+1}H"] = D_entropy[j]

        return g

    out = out.groupby(entity_col, group_keys=False).apply(_per_group)
    return out


def _select_topk_features_with_xgb(
    X_tr: pd.DataFrame, y_tr: np.ndarray,
    X_va: pd.DataFrame, y_va: np.ndarray,
    feature_names: Optional[List[str]] = None,
    k_list: List[int] = [10, 20, 30, 40, 50, 60, 80, 100],
    seed: int = 42
) -> Tuple[List[str], Dict[str, float]]:
    """
    Select top-k numeric/bool features by XGB importance.
    """
    # 1) Keep only numeric and boolean columns
    X_tr = X_tr.select_dtypes(include=[np.number, "bool"]).copy()
    X_va = X_va[X_tr.columns].copy()  # align columns

    if feature_names is None:
        feature_names = list(X_tr.columns)
    else:
        feature_names = [f for f in feature_names if f in X_tr.columns]

    # 2) Base model to get importances
    base = XGBRegressor(
        random_state=seed,
        n_estimators=450,
        max_depth=None,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        min_child_weight=1.0,
        reg_alpha=0.0,
        reg_lambda=1.0,
        tree_method="hist",
        objective="reg:squarederror",
        n_jobs=-1,
        eval_metric="rmse",
    )
    base.fit(X_tr, y_tr, eval_set=[(X_va, y_va)], verbose=False)

    importances = getattr(base, "feature_importances_", None)
    if importances is None or len(importances) != len(feature_names):
        importances = np.zeros(len(feature_names), dtype=float)

    order = np.argsort(-importances, kind="stable")
    ranked_feats = [feature_names[i] for i in order]

    # 3) Sweep K values
    best_k, best_r2 = None, -np.inf
    for k in sorted(set(int(x) for x in k_list if x > 0)):
        k = min(k, len(ranked_feats))
        sel = ranked_feats[:k]

        model = XGBRegressor(
            random_state=seed,
            n_estimators=500,
            max_depth=None,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            min_child_weight=1.0,
            reg_alpha=0.0,
            reg_lambda=1.0,
            tree_method="hist",
            objective="reg:squarederror",
            n_jobs=-1,
            eval_metric="rmse",
        )
        model.fit(X_tr[sel], y_tr, eval_set=[(X_va[sel], y_va)], verbose=False)
        pred_va = model.predict(X_va[sel])
        r2 = r2_score(y_va, pred_va)

        if r2 > best_r2:
            best_r2, best_k = r2, k

    selected = ranked_feats[:best_k]
    return selected, {"best_k": float(best_k), "best_r2": float(best_r2)}


wavelet_cols = [
    "prc",             
    "market_equity",
    "dolvol_126d",
    "turnover_126d",
    "ret_eom",
    "ret_12_1",
    "vol_12m",
    "rvol_21d",
    "beta_60m",
    "ivol_capm_21d"
]

df_dwt = add_dwt_features(
    data_df_vol,
    entity_col="permno",     # or "cusip" depending on your panel index
    time_col="date",
    series_cols=wavelet_cols,
    window=36,               # e.g. 36 months trailing for monthly data
    wavelet="db4",
    level=None,              # auto (≈ log2(window), capped at 4)
    suffix="dwt"
)

print(list(df_dwt.columns))




train = df_dwt[(df_dwt ['date'] >= pd.Timestamp("2000-01-01")) & (df_dwt['date'] < pd.Timestamp("2015-01-01"))].copy()
#val = df_dwt[(df_dwt['date'] >= pd.Timestamp("2015-01-01")) & (df_dwt['date'] < pd.Timestamp("2016-01-01"))].copy()
test = df_dwt[(df_dwt['date'] >= pd.Timestamp("2015-01-01")) & (df_dwt['date'] < pd.Timestamp("2024-01-01"))].copy()

X_train = train.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
y_train = train['stock_exret']
# X_val = val.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
# y_val = val['stock_exret']
X_test = test.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
y_test = test['stock_exret']

print(list(X_train.columns))

def plot_hidden_states_matplotlib(states, prices_data, start_date=None, end_date=None):
    """
    Plot price data with hidden regime states directly visible on the price graph.
    Each state is shown in a different color.
    
    Parameters:
        states (np.ndarray): Array of hidden state labels.
        prices_data (pd.Series or pd.DataFrame): Time-indexed price data (1D).
        start_date (str or pd.Timestamp, optional): Start date for the plot.
        end_date (str or pd.Timestamp, optional): End date for the plot.
    """
    # Ensure index is datetime
    prices_data = prices_data.copy()
    prices_data.index = pd.to_datetime(prices_data.index)

    # Apply date filtering if specified
    if start_date:
        start_date = pd.to_datetime(start_date)
        prices_data = prices_data[prices_data.index >= start_date]
        states = states[-len(prices_data):]

    if end_date:
        end_date = pd.to_datetime(end_date)
        prices_data = prices_data[prices_data.index <= end_date]
        states = states[-len(prices_data):]

    if len(prices_data) != len(states):
        raise ValueError("Length of `states` must match filtered `prices_data`")

    unique_states = np.unique(states)
    colormap = plt.cm.get_cmap('tab20', len(unique_states))
    colors = [colormap(i) for i in range(len(unique_states))]

    fig, ax = plt.subplots(figsize=(14, 6))

    for i, state in enumerate(unique_states):
        mask = (states == state)
        ax.plot(prices_data.index[mask], prices_data.values[mask],
                linestyle='none', marker='o', markersize=4,
                color=colors[i], label=f'Regime {state}')

    ax.set_title("SPX Price Colored by Regime States")
    ax.set_xlabel("Date")
    ax.set_ylabel("Price")
    ax.legend()
    ax.grid(True)

    plt.tight_layout()
    plt.show()

sp500 = yf.download("^GSPC", start="2000-01-01", end="2024-01-01")
sp500.head()

prices_ma = sp500.rolling(20).mean().dropna()
prices_log_ret = np.log(prices_ma / prices_ma.shift(1)).dropna()

params_hmm = {
    'n_components': 2,
    'random_state': 42,
    'n_iter': 1000,
    'covariance_type': 'full'
}

def prepare_data(prices, instrument, ma=20):
    """
    Prepare data for regime detection by calculating moving average and log returns

    Parameters:
    prices: DataFrame with price data
    instrument: Column name for the instrument
    ma: Moving average window (default 20)

    Returns:
    prices_array: Numpy array for model input
    """
    # Moving average
    prices[f'{instrument}_ma'] = prices[instrument].rolling(ma).mean()

    # Log return of moving average
    prices[f'{instrument}_log_return'] = np.log(
        prices[f'{instrument}_ma'] / prices[f'{instrument}_ma'].shift(1)
    )

    # Drop NaNs after all calculations
    prices_cleaned = prices.dropna(subset=[f'{instrument}_log_return'])

    # Prepare array for model input
    prices_array = np.array([[val] for val in prices_cleaned[f'{instrument}_log_return'].values])

    return prices_array

spx = sp500[('Close', '^GSPC')].to_frame(name='SPX')
prices_log_ret = prepare_data(spx, 'SPX', ma=20)
hmm_model = GaussianHMM(**params_hmm).fit(prices_log_ret)
hmm_states = hmm_model.predict(prices_log_ret)
sp500.index = pd.to_datetime(sp500.index)
plot_hidden_states_matplotlib(np.array(hmm_states), spx[[f'SPX']].iloc[20:])


cat_cols = X_train.select_dtypes(include=['object']).columns.tolist()
X_train = X_train.copy()
X_test  = X_test.copy()

for c in cat_cols:
    X_train[c] = X_train[c].astype('category')

# Align categories between train and test
for c in cat_cols:
    X_test[c] = X_test[c].astype('category')
    X_test[c] = X_test[c].cat.set_categories(X_train[c].cat.categories)

selected_features, meta = _select_topk_features_with_xgb(
    X_train, y_train.values,
    X_test, y_test.values,
    list(X_train.columns),
    k_list=[10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 120, 150, 200]
)

print("Selected features:", selected_features)

print(len(selected_features))

X_train_sel = X_train[selected_features]
X_test_sel  = X_test[selected_features]

xgb_reg = XGBRegressor(
    n_estimators=200,
    learning_rate=0.1,
    max_depth=None,
    subsample=0.8,
    colsample_bytree=0.8,
    random_state=42,
    n_jobs=-1,
    tree_method='hist',
    enable_categorical=True,
    eval_metric='rmse'
)

xgb_reg.fit(X_train_sel, y_train)

y_pred = xgb_reg.predict(X_test_sel)
mse = mean_squared_error(y_test, y_pred)
r_2 = r2_score(y_test, y_pred)
print(f"XGB Regressor Test MSE: {mse}, R^2: {r_2}")

mlp = MLPRegressor(

)

mlp.fit(X_train, y_train)

y_pred = mlp.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r_2 = 1 - mse / np.var(y_test)
print(f"MLP Regressor Test MSE: {mse}, R^2: {r_2}")

lgbm_reg = LGBMRegressor(
    
)

lgbm_reg.fit(X_train, y_train)

y_pred = lgbm_reg.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r_2 = 1 - mse / np.var(y_test)
print(f"LGBM Regressor Test MSE: {mse}, R^2: {r_2}")

hgbr = HistGradientBoostingRegressor(
    
)

hgbr.fit(X_train, y_train)

y_pred = hgbr.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r_2 = 1 - mse / np.var(y_test)
print(f"HGBR Regressor Test MSE: {mse}, R^2: {r_2}")

random_forest = RandomForestRegressor(
    
)

random_forest.fit(X_train, y_train)

y_pred = random_forest.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r_2 = 1 - mse / np.var(y_test)
print(f"Random Forest Regressor Test MSE: {mse}, R^2: {r_2}")

out_rows = []
start = pd.to_datetime("2000-01-01")
oos_end = pd.to_datetime("2024-01-01")
counter = 0
while((start + pd.DateOffset(years=11 + counter)) <= oos_end):
        cutoff_train_end = start + pd.DateOffset(years=8 + counter)
        cutoff_val_end = start + pd.DateOffset(years=10 + counter)
        cutoff_test_end = start + pd.DateOffset(years=11 + counter)

        train = df_dwt[(df_dwt ['date'] >= start) & (df_dwt['date'] < cutoff_train_end)].copy()
        val = df_dwt[(df_dwt['date'] >= cutoff_train_end) & (df_dwt['date'] < cutoff_val_end)].copy()
        test = df_dwt[(df_dwt['date'] >= cutoff_val_end) & (df_dwt['date'] < cutoff_test_end)].copy()

        X_train = train.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
        y_train = train['stock_exret']
        X_val = val.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
        y_val = val['stock_exret']
        X_test = test.drop(columns=['date', 'year', 'month', 'permno', 'stock_exret'])
        y_test = test['stock_exret']
        X_train_sel = X_train[selected_features]
        X_test_sel  = X_test[selected_features]

        xgb_reg = XGBRegressor(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=None,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                tree_method='hist',
                enable_categorical=True,
                eval_metric='rmse'
        )

        xgb_reg.fit(X_train_sel, y_train)

        y_pred = xgb_reg.predict(X_test_sel)
        block = test[["year","month","date","permno","stock_exret"]].copy()
        block["year"]  = block["year"].astype(int)
        block["month"] = block["month"].astype(int)
        block["date"] = pd.to_datetime(
            {"year": block["year"], "month": block["month"], "day": 1}
        )
        block["xgb"] = y_pred
        out_rows.append(block)

        counter += 1

pred_out = pd.concat(out_rows, ignore_index=True)
pred_out = pred_out.drop_duplicates(subset=["year","month","permno"]).sort_values(["year","month","permno"])
pred_out.to_csv("output.csv", index=False)

y_true = pred_out["stock_exret"].values
y_hat  = pred_out["xgb"].values
oos_r2 = 1 - np.sum((y_true - y_hat)**2) / np.sum(y_true**2)
print(f"OOS R^2 (zero benchmark): {oos_r2:.4f}")

%run portfolio_analysis_hackathon.py --model xgb

# --------------------- Utilities ---------------------

def ym_int(dt_series: pd.Series) -> pd.Series:
    dt = pd.to_datetime(dt_series)
    return (dt.dt.year * 100 + dt.dt.month).astype(int)

def trailing_covariance(returns_wide: pd.DataFrame, end_idx: int, window: int, cols: list,
                        shrink_gamma: float = 0.5, ridge: float = 1e-6) -> np.ndarray:
    """
    returns_wide: rows = months (sorted), cols = permno, values = stock_exret
    end_idx: index of the current month row in returns_wide.index (exclusive)
    window: number of trailing months to use
    cols: candidate permnos (subset of columns)
    shrink_gamma: 0..1, 1=all diagonal
    """
    start = max(0, end_idx - window)
    if start >= end_idx:
        # No history: fall back to diagonal with unit variance
        d = np.ones(len(cols))
        return np.diag(d + ridge)

    R = returns_wide.loc[returns_wide.index[start:end_idx-1], cols]  # history only
    # Fill missing with column means (0 fallback)
    col_means = R.mean(axis=0).fillna(0.0)
    R = R.fillna(col_means)

    if R.shape[0] < 2:
        d = np.maximum(R.var(ddof=1, axis=0).values, 1e-6)
        return np.diag(d + ridge)

    S = np.cov(R.values, rowvar=False)  # sample covariance (N x N)
    # Diagonal target
    D = np.diag(np.diag(S))
    Sigma = (1 - shrink_gamma) * S + shrink_gamma * D
    # Ridge for PSD & conditioning
    Sigma = Sigma + ridge * np.eye(Sigma.shape[0])
    return Sigma

def mv_optimize(mu: np.ndarray, Sigma: np.ndarray, gross: float = 1.0, cap: float = 0.03,
                lam: float = 10.0, solver: str = "ECOS") -> np.ndarray:
    """
    Solve: max_w  mu^T w - lam * w^T Sigma w
    s.t. sum(w)=0, ||w||_1 <= gross, |w_i| <= cap
    Implemented via decomposition w = u - v, u,v >= 0 to keep constraints well-behaved.
    """
    n = len(mu)
    if n == 0:
        return np.array([])

    # Normalize scale for numeric stability
    var_scale = np.median(np.diag(Sigma)) if np.isfinite(np.diag(Sigma)).all() else 1.0
    if var_scale <= 0: var_scale = 1.0
    Sigma_s = Sigma / var_scale
    mu_s = mu / np.sqrt(var_scale)
    lam_s = lam  # lam is already user-chosen trade-off; keep as-is after scaling

    u = cp.Variable(n, nonneg=True)  # long
    v = cp.Variable(n, nonneg=True)  # short
    w = u - v

    objective = cp.Maximize(mu_s @ w - lam_s * cp.quad_form(w, Sigma_s))
    constraints = [
        cp.sum(u) - cp.sum(v) == 0.0,                  # market neutral
        cp.sum(u) + cp.sum(v) <= gross,                # gross <= 1
        u <= cap, v <= cap                             # per-name caps (abs bound)
    ]
    prob = cp.Problem(objective, constraints)
    try:
        prob.solve(solver=solver, verbose=False, warm_start=True)
    except Exception as e:
        warnings.warn(f"CVX solve failed with {e}; trying SCS.")
        prob.solve(solver="SCS", verbose=False, warm_start=True)

    if w.value is None:
        # Fall back: proportional to mu with net/gross constraints
        w_raw = mu.copy()
        if np.allclose(w_raw, 0):
            return np.zeros(n)
        w_raw = w_raw / np.sum(np.abs(w_raw)) * gross
        w_raw = np.clip(w_raw, -cap, cap)
        # enforce net ~ 0 by de-meaning
        w_raw = w_raw - w_raw.mean()
        # re-normalize gross
        s = np.sum(np.abs(w_raw))
        return w_raw * (gross / s) if s > 0 else w_raw
    return np.array(w.value).flatten()

# --------------------- Monthly MV Builder ---------------------

def mv_longshort_month(g_month: pd.DataFrame, R_wide: pd.DataFrame, month_pos: int,
                       model_col: str = "xgb", K: int = 100, gross: float = 1.0,
                       cap: float = 0.03, lam: float = 10.0, risk_window: int = 12,
                       shrink_gamma: float = 0.5, ridge: float = 1e-6) -> pd.DataFrame:
    """
    Build one month's MV portfolio.
    g_month: rows for a single (year, month) with columns [permno, stock_exret, model_col]
    R_wide: pivot of returns (index = sorted unique months, columns=permno)
    month_pos: integer position of this (year,month) in R_wide.index
    Returns: DataFrame [permno, w]
    """
    # 1) Universe: top-K by |mu|
    g = g_month[["permno", model_col]].dropna().copy()
    if g.empty: return pd.DataFrame(columns=["permno","w"])
    g["abs_mu"] = g[model_col].abs()
    g = g.sort_values("abs_mu", ascending=False).head(K)
    permnos = g["permno"].tolist()
    mu = g[model_col].values.astype(float)

    # 2) Covariance from trailing window
    Sigma = trailing_covariance(R_wide, end_idx=month_pos, window=risk_window, cols=permnos,
                                shrink_gamma=shrink_gamma, ridge=ridge)

    # 3) Solve MV
    w = mv_optimize(mu, Sigma, gross=gross, cap=cap, lam=lam)

    return pd.DataFrame({"permno": permnos, "w": w})

def mv_run_from_output(output_csv: str = "output.csv", model_col: str = "xgb",
                       K: int = 100, gross: float = 1.0, cap: float = 0.03,
                       lam: float = 10.0, risk_window: int = 12,
                       shrink_gamma: float = 0.5, ridge: float = 1e-6,
                       mkt_path: str = "mkt_ind.csv"):
    """
    Main runner: builds monthly MV portfolios over the OOS period from output.csv
    and prints deck metrics. Returns (monthly_df, weights_dict).
    """
    df = pd.read_csv(output_csv, parse_dates=["date"])
    for c in ["year","month","permno","stock_exret", model_col]:
        if c not in df.columns:
            raise ValueError(f"Missing column in {output_csv}: {c}")

    # Ensure clean types & month index
    df["year"] = df["year"].astype(int)
    df["month"] = df["month"].astype(int)
    df["ym"] = ym_int(df["date"])

    # Wide returns matrix (sorted by month)
    month_order = sorted(df["ym"].unique())
    R_wide = (df.pivot_table(index="ym", columns="permno", values="stock_exret", aggfunc="mean")
                .reindex(month_order))

    # Build month by month
    port_rows, weight_turnover, name_turnover = [], [], []
    prev_w = None
    prev_names = None
    weights_by_month = {}

    for month_pos, ym in enumerate(month_order):
        g = df[df["ym"] == ym].copy()
        if g.empty: 
            continue

        w_df = mv_longshort_month(
            g_month=g, R_wide=R_wide, month_pos=month_pos,
            model_col=model_col, K=K, gross=gross, cap=cap, lam=lam,
            risk_window=risk_window, shrink_gamma=shrink_gamma, ridge=ridge
        )
        # Realized return
        merged = w_df.merge(g[["permno","stock_exret"]], on="permno", how="left")
        r_m = float((merged["w"] * merged["stock_exret"]).sum())

        # Weight-based turnover: 0.5 * sum |w_t - w_{t-1}|
        if prev_w is None:
            wt_to = np.nan
        else:
            # align by permno
            cur = w_df.set_index("permno")["w"]
            pw  = prev_w.reindex(cur.index).fillna(0.0)
            cw  = cur.reindex(prev_w.index).fillna(0.0)
            wt_to = 0.5 * float(np.sum(np.abs(cw.values - pw.values)))
        prev_w = w_df.set_index("permno")["w"]

        # Name-based turnover: fraction of names replaced
        names = set(w_df["permno"].tolist())
        if prev_names is None:
            nm_to = np.nan
        else:
            stayed = len(prev_names & names)
            nm_to = (len(names) - stayed) / len(names) if len(names) else np.nan
        prev_names = names

        y, m = int(ym // 100), int(ym % 100)
        port_rows.append({"year": y, "month": m, "port_ret": r_m})
        weight_turnover.append(wt_to)
        name_turnover.append(nm_to)
        weights_by_month[ym] = w_df.copy()

    monthly = pd.DataFrame(port_rows).sort_values(["year","month"]).reset_index(drop=True)
    monthly["to_weight"] = weight_turnover
    monthly["to_names"] = name_turnover

    # Basic stats
    ret = monthly["port_ret"]
    sharpe = (ret.mean() / ret.std() * np.sqrt(12)) if ret.std() > 0 else np.nan
    ann_ret = ((1 + ret).prod() ** (12 / len(ret)) - 1) if len(ret) else np.nan
    ann_vol = (ret.std() * np.sqrt(12)) if len(ret) > 1 else np.nan
    curve = (1 + ret).cumprod()
    mdd = ((curve.cummax() - curve) / curve.cummax()).max() if len(curve) else np.nan
    max_1m_loss = ret.min() if len(ret) else np.nan
    avg_to_weight = float(np.nanmean(monthly["to_weight"].values))
    avg_to_names  = float(np.nanmean(monthly["to_names"].values))

    print("\n=== MV Long–Short Portfolio (monthly rebal) ===")
    print(f"Months:               {len(monthly)}")
    print(f"Annualized return:    {ann_ret:.4f}")
    print(f"Annualized vol:       {ann_vol:.4f}")
    print(f"Sharpe (ann.):        {sharpe:.3f}")
    print(f"Max drawdown:         {mdd:.4f}")
    print(f"Max 1m loss:          {max_1m_loss:.4f}")
    print(f"Turnover (weights):   {avg_to_weight:.3f}")
    print(f"Turnover (names):     {avg_to_names:.3f}")

    # CAPM alpha & information ratio
    try:
        mkt = pd.read_csv(mkt_path)
        monthly_alpha = monthly.merge(mkt, on=["year","month"], how="inner")
        import statsmodels.formula.api as sm
        nw = sm.ols("port_ret ~ mkt_rf", data=monthly_alpha).fit(
            cov_type="HAC", cov_kwds={"maxlags":3}, use_t=True
        )
        alpha_m = nw.params["Intercept"]; alpha_t = nw.tvalues["Intercept"]
        alpha_ann = alpha_m * 12
        info_ratio = alpha_m / np.sqrt(nw.mse_resid) * np.sqrt(12)

        print("\nCAPM (HAC, 3 lags)")
        print(f"Alpha (monthly):      {alpha_m:.4f}  t={alpha_t:.2f}")
        print(f"Alpha (annual):       {alpha_ann:.4f}")
        print(f"Information ratio:    {info_ratio:.3f}")
    except Exception as e:
        warnings.warn(f"Alpha/IR skipped (need mkt_ind.csv with [year,month,mkt_rf]): {e}")

    return monthly, weights_by_month